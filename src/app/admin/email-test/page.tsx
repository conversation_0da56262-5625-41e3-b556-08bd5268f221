'use client'

import { useState } from 'react'

export default function EmailTestPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [preview, setPreview] = useState<any>(null)

  const handleSendWeeklySummaries = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/weekly-summaries', {
        method: 'POST',
      })
      const data = await response.json()
      setResults(data)
    } catch (error) {
      console.error('Error sending weekly summaries:', error)
      setResults({ error: 'Failed to send weekly summaries' })
    } finally {
      setLoading(false)
    }
  }

  const handlePreviewSummaries = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/weekly-summaries', {
        method: 'GET',
      })
      const data = await response.json()
      setPreview(data)
    } catch (error) {
      console.error('Error getting preview:', error)
      setPreview({ error: 'Failed to get preview' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">📧 Email System Test</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <button
            onClick={handlePreviewSummaries}
            disabled={loading}
            className="p-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg font-semibold transition-colors"
          >
            {loading ? 'Loading...' : '👀 Preview Weekly Summaries'}
          </button>
          
          <button
            onClick={handleSendWeeklySummaries}
            disabled={loading}
            className="p-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-lg font-semibold transition-colors"
          >
            {loading ? 'Sending...' : '📧 Send Weekly Summaries'}
          </button>
        </div>

        {preview && (
          <div className="mb-8 p-6 bg-gray-800 rounded-lg">
            <h2 className="text-xl font-bold mb-4">📋 Preview Results</h2>
            <pre className="text-sm overflow-auto bg-gray-700 p-4 rounded">
              {JSON.stringify(preview, null, 2)}
            </pre>
          </div>
        )}

        {results && (
          <div className="mb-8 p-6 bg-gray-800 rounded-lg">
            <h2 className="text-xl font-bold mb-4">📧 Send Results</h2>
            <pre className="text-sm overflow-auto bg-gray-700 p-4 rounded">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        )}

        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-bold mb-4">📖 Enhanced Email System</h2>
          <div className="space-y-4 text-sm">
            <div>
              <h3 className="font-semibold text-green-400">✅ Low Stock Alerts (Immediate)</h3>
              <p className="text-gray-300">Sent immediately when item quantity drops to minimum level. Maximum once per 24 hours per item.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-blue-400">📊 Weekly Cycle Count Summary</h3>
              <p className="text-gray-300">Single weekly email listing all items that need to be counted. Replaces individual cycle count emails.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-red-400">🛒 Weekly Purchase Reminders</h3>
              <p className="text-gray-300">Weekly reminder emails for items that are at minimum stock level and need to be purchased.</p>
            </div>
            
            <div>
              <h3 className="font-semibold text-purple-400">🚫 Duplicate Prevention</h3>
              <p className="text-gray-300">System prevents duplicate notifications and manages different email types properly with database tracking.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
