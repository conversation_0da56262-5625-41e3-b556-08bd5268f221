import { NextRequest, NextResponse } from 'next/server'
import { emailAlerts } from '@/lib/email'
import { itemsApi } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { itemUpc, alertType } = body

    if (!itemUpc) {
      return NextResponse.json(
        { error: 'Item UPC is required' },
        { status: 400 }
      )
    }

    if (!alertType || !['low_stock', 'cycle_count', 'both'].includes(alertType)) {
      return NextResponse.json(
        { error: 'Valid alert type is required (low_stock, cycle_count, or both)' },
        { status: 400 }
      )
    }

    // Get the item from the database
    const item = await itemsApi.getByUpc(itemUpc)
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }

    const results = {
      lowStockSent: false,
      cycleCountSent: false,
      errors: [] as string[]
    }

    // Send appropriate alerts
    if (alertType === 'low_stock' || alertType === 'both') {
      try {
        results.lowStockSent = await emailAlerts.sendLowStockAlert(item)
      } catch (error) {
        console.error('Error sending low stock alert:', error)
        results.errors.push('Failed to send low stock alert')
      }
    }

    if (alertType === 'cycle_count' || alertType === 'both') {
      try {
        results.cycleCountSent = await emailAlerts.sendCycleCountAlert(item)
      } catch (error) {
        console.error('Error sending cycle count alert:', error)
        results.errors.push('Failed to send cycle count alert')
      }
    }

    return NextResponse.json({
      success: true,
      item: {
        upc: item.upc,
        description: item.description
      },
      results
    })

  } catch (error) {
    console.error('❌ Email API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint to check if alerts should be sent for an item
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const itemUpc = searchParams.get('upc')

    if (!itemUpc) {
      return NextResponse.json(
        { error: 'Item UPC is required' },
        { status: 400 }
      )
    }

    // Get the item from the database
    const item = await itemsApi.getByUpc(itemUpc)
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }

    // Import alert checkers dynamically to avoid server-side issues
    const { alertCheckers } = await import('@/lib/email')

    const shouldSendLowStock = await alertCheckers.shouldSendLowStockAlert(item)
    const shouldSendCycleCount = await alertCheckers.shouldSendCycleCountAlert(item)

    return NextResponse.json({
      item: {
        upc: item.upc,
        description: item.description,
        quantity: item.quantity,
        minimum: item.minimum,
        date_last_counted: item.date_last_counted
      },
      alerts: {
        shouldSendLowStock,
        shouldSendCycleCount,
        lowStockReason: shouldSendLowStock ? 
          `Quantity (${item.quantity || 0}) is at or below minimum (${item.minimum})` : 
          'Quantity is above minimum or no minimum set',
        cycleCountReason: shouldSendCycleCount ?
          'Item needs cycle count based on last counted date' :
          'Item was recently counted or cycle count not due'
      }
    })

  } catch (error) {
    console.error('❌ Email check API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
