import { NextRequest, NextResponse } from 'next/server'
import { itemsApi } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { itemUpc } = body

    if (!itemUpc) {
      return NextResponse.json(
        { error: 'Item UPC is required' },
        { status: 400 }
      )
    }

    console.log(`📧 API: Checking alerts for item UPC: ${itemUpc}`)

    // Get the item from the database
    const item = await itemsApi.getByUpc(itemUpc)
    if (!item) {
      return NextResponse.json(
        { error: 'Item not found' },
        { status: 404 }
      )
    }

    console.log(`📧 API: Found item: ${item.description} (Qty: ${item.quantity}, Min: ${item.minimum})`)

    // Import and run email alerts
    console.log(`📧 API: Importing email alerts module...`)
    const { emailAlerts } = await import('@/lib/email')
    console.log(`📧 API: Running email alert checks...`)
    await emailAlerts.checkAndSendAlerts(item)
    console.log(`📧 API: Email alert checks completed.`)

    return NextResponse.json({
      success: true,
      message: 'Email alerts checked successfully',
      item: {
        upc: item.upc,
        description: item.description,
        quantity: item.quantity,
        minimum: item.minimum
      }
    })

  } catch (error) {
    console.error('❌ Check alerts API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
