import { NextRequest, NextResponse } from 'next/server'
import { emailAlerts } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    console.log('📧 API: Starting weekly summaries...')

    const results = await emailAlerts.sendWeeklySummaries()

    return NextResponse.json({
      success: true,
      message: 'Weekly summaries processed successfully',
      results: {
        cycleSummary: {
          sent: results.cycleSummary,
          description: results.cycleSummary 
            ? 'Weekly cycle count summary sent successfully'
            : 'No cycle count summary needed (already sent recently or no items need counting)'
        },
        purchaseReminder: {
          sent: results.purchaseReminder,
          description: results.purchaseReminder
            ? 'Weekly purchase reminder sent successfully'
            : 'No purchase reminder needed (already sent recently or no items at minimum stock)'
        }
      }
    })

  } catch (error) {
    console.error('❌ Weekly summaries API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET endpoint to check what would be included in weekly summaries
export async function GET(request: NextRequest) {
  try {
    console.log('📧 API: Checking weekly summaries preview...')

    // Import weekly summaries functions
    const { weeklySummaries } = await import('@/lib/email')

    // Get items that would be included
    const cycleCountItems = await weeklySummaries.getItemsNeedingCycleCount()
    const purchaseReminderItems = await weeklySummaries.getItemsAtMinimumStock()

    // Check if summaries should be sent
    const shouldSendCycleSummary = await weeklySummaries.shouldSendWeeklySummary('cycle_count')
    const shouldSendPurchaseReminder = await weeklySummaries.shouldSendWeeklySummary('purchase_reminder')

    return NextResponse.json({
      preview: {
        cycleSummary: {
          shouldSend: shouldSendCycleSummary,
          itemCount: cycleCountItems.length,
          items: cycleCountItems.map(item => ({
            upc: item.upc,
            description: item.description,
            quantity: item.quantity,
            lastCounted: item.date_last_counted,
            cycleInterval: item.count_interval_days || 30
          }))
        },
        purchaseReminder: {
          shouldSend: shouldSendPurchaseReminder,
          itemCount: purchaseReminderItems.length,
          items: purchaseReminderItems.map(item => ({
            upc: item.upc,
            description: item.description,
            quantity: item.quantity,
            minimum: item.minimum,
            suggestedPurchaseQty: item.suggested_purchase_qty,
            purchaseLink: item.purchase_link
          }))
        }
      }
    })

  } catch (error) {
    console.error('❌ Weekly summaries preview API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
