"use client";

import { useState, useEffect, useRef } from "react";
import { itemsApi, Item } from "@/lib/supabase";

interface SearchInputProps {
  onItemSelect: (item: Item) => void;
  onNewItem: (upc: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  value?: string;
  onChange?: (value: string) => void;
}

export default function SearchInput({
  onItemSelect,
  onNewItem,
  placeholder = "Search items or scan barcode...",
  autoFocus = false,
  value,
  onChange,
}: SearchInputProps) {
  const [query, setQuery] = useState(value || "");
  const [suggestions, setSuggestions] = useState<Item[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Sync external value with internal query state
  useEffect(() => {
    if (value !== undefined && value !== query) {
      setQuery(value);
    }
  }, [value]);

  useEffect(() => {
    const searchItems = async () => {
      if (query.trim().length === 0) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setIsLoading(true);
      try {
        const results = await itemsApi.search(query.trim());
        setSuggestions(results);
        setShowSuggestions(true);
        setSelectedIndex(0);
      } catch (error) {
        console.error("Search error:", error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(searchItems, 150);
    return () => clearTimeout(debounceTimer);
  }, [query]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) {
      // No special handling for Enter key when no suggestions
      return;
    }

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case "Enter":
        e.preventDefault();
        if (suggestions[selectedIndex]) {
          handleItemSelect(suggestions[selectedIndex]);
        }
        break;
      case "Escape":
        setShowSuggestions(false);
        break;
    }
  };

  const handleItemSelect = (item: Item) => {
    setQuery("");
    onChange?.("");
    setSuggestions([]);
    setShowSuggestions(false);
    onItemSelect(item);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setQuery(newValue);
    onChange?.(newValue);
  };

  const handleBlur = () => {
    setTimeout(() => setShowSuggestions(false), 150);
  };

  return (
    <div className="relative w-full">
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          onFocus={() => query && setShowSuggestions(true)}
          placeholder={placeholder}
          className="w-full px-4 py-3 pl-12 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
        />

        <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
          {isLoading ? (
            <div className="animate-spin h-5 w-5 border-2 border-purple-500 border-t-transparent rounded-full"></div>
          ) : (
            <svg
              className="h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          )}
        </div>
      </div>

      {showSuggestions && (
        <div className="absolute z-50 w-full mt-1 bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-80 overflow-y-auto">
          {suggestions.length > 0 ? (
            suggestions.map((item, index) => (
              <div
                key={item.id}
                onClick={() => handleItemSelect(item)}
                className={`px-4 py-3 cursor-pointer border-b border-gray-700 last:border-b-0 ${
                  index === selectedIndex
                    ? "bg-purple-600 text-white"
                    : "text-gray-300 hover:bg-gray-700"
                }`}
              >
                <div className="font-medium">
                  {item.suggested_brand_model || item.description}
                </div>
                {item.suggested_brand_model && (
                  <div className="text-sm text-gray-400 mt-1">
                    {item.description}
                  </div>
                )}
                <div className="text-xs text-gray-500 mt-1">
                  UPC: {item.upc}
                </div>
              </div>
            ))
          ) : query.trim() && !isLoading ? (
            <div className="px-4 py-3">
              <div className="text-gray-400 mb-2">
                No items found matching &quot;{query.trim()}&quot;
              </div>
              <div
                onClick={() => onNewItem(query.trim())}
                className="cursor-pointer text-purple-400 hover:text-purple-300 transition-colors"
              >
                📦 Add new item with this search term
              </div>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
}
