'use client'

import { useState } from 'react'
import { Item, itemsApi } from '@/lib/supabase'

interface ItemFormProps {
  item?: Item | null
  initialUpc?: string
  onSave: (item: Item) => void
  onCancel: () => void
}

export default function ItemForm({ item, initialUpc, onSave, onCancel }: ItemFormProps) {
  const [formData, setFormData] = useState({
    upc: initialUpc || item?.upc || '',
    description: item?.description || '',
    quantity: item?.quantity?.toString() || '',  // Changed from current_quantity
    product_image: item?.product_image || '',
    purchase_link: item?.purchase_link || '',
    suggested_brand_model: item?.suggested_brand_model || '',
    suggested_purchase_qty: item?.suggested_purchase_qty?.toString() || '',
    minimum: item?.minimum?.toString() || '',
    notes: item?.notes || '',
    count_interval_days: item?.count_interval_days?.toString() || ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const isEditing = !!item

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.upc.trim()) {
      newErrors.upc = 'UPC is required'
    }
    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || isSubmitting) return

    setIsSubmitting(true)
    try {
      const itemData = {
        upc: formData.upc.trim(),
        description: formData.description.trim(),
        quantity: formData.quantity ? parseInt(formData.quantity) : undefined,  // Changed from current_quantity
        product_image: formData.product_image.trim() || undefined,
        purchase_link: formData.purchase_link.trim() || undefined,
        suggested_brand_model: formData.suggested_brand_model.trim() || undefined,
        suggested_purchase_qty: formData.suggested_purchase_qty ? parseInt(formData.suggested_purchase_qty) : undefined,
        minimum: formData.minimum ? parseInt(formData.minimum) : undefined,
        notes: formData.notes.trim() || undefined,
        count_interval_days: formData.count_interval_days ? parseInt(formData.count_interval_days) : undefined
      }

      let savedItem: Item
      if (isEditing) {
        savedItem = await itemsApi.update(item.id, itemData)
      } else {
        savedItem = await itemsApi.create(itemData)
      }

      onSave(savedItem)
    } catch (error) {
      console.error('Save error:', error)
      alert('Failed to save item. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const formFields = [
    {
      key: 'upc',
      label: 'UPC *',
      type: 'text',
      placeholder: 'e.g., 012345678905',
      required: true
    },
    {
      key: 'description',
      label: 'Description *',
      type: 'text',
      placeholder: 'Item description',
      required: true
    },
    {
      key: 'quantity',  // Changed from current_quantity
      label: 'Current Quantity',
      type: 'number',
      placeholder: '0'
    },
    {
      key: 'suggested_brand_model',
      label: 'Brand and Model',
      type: 'text',
      placeholder: 'e.g., Apple iPhone 15 Pro'
    },
    {
      key: 'product_image',
      label: 'Product Image URL',
      type: 'url',
      placeholder: 'https://example.com/image.jpg'
    },
    {
      key: 'purchase_link',
      label: 'Purchase Link',
      type: 'url',
      placeholder: 'https://example.com/product'
    },
    {
      key: 'suggested_purchase_qty',
      label: 'Suggested Purchase Quantity',
      type: 'number',
      placeholder: '1'
    },
    {
      key: 'minimum',
      label: 'Minimum Stock Level',
      type: 'number',
      placeholder: '5'
    },
    {
      key: 'count_interval_days',
      label: 'Count Interval (Days)',
      type: 'number',
      placeholder: '30'
    },
    {
      key: 'notes',
      label: 'Notes',
      type: 'textarea',
      placeholder: 'Additional notes...'
    }
  ]

  return (
    <div className="max-w-2xl mx-auto p-4 bg-gray-800 rounded-lg border border-gray-600">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-white">
          {isEditing ? '✏️ Edit Item' : '📦 Create New Item'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {formFields.map((field) => (
          <div key={field.key}>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              {field.label}
            </label>
            {field.type === 'textarea' ? (
              <textarea
                value={formData[field.key as keyof typeof formData]}
                onChange={(e) => handleChange(field.key, e.target.value)}
                placeholder={field.placeholder}
                rows={3}
                className={`w-full px-3 py-2 bg-gray-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors[field.key] ? 'border-red-500' : 'border-gray-600'
                }`}
              />
            ) : (
              <input
                type={field.type}
                value={formData[field.key as keyof typeof formData]}
                onChange={(e) => handleChange(field.key, e.target.value)}
                placeholder={field.placeholder}
                min={field.type === 'number' ? '0' : undefined}
                className={`w-full px-3 py-2 bg-gray-700 border rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                  errors[field.key] ? 'border-red-500' : 'border-gray-600'
                }`}
              />
            )}
            {errors[field.key] && (
              <p className="mt-1 text-sm text-red-400">{errors[field.key]}</p>
            )}
          </div>
        ))}

        <div className="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex-1 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 text-white font-medium rounded transition-colors"
          >
            {isSubmitting ? 'Saving...' : (isEditing ? 'Update Item' : 'Create Item')}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white font-medium rounded transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  )
}
