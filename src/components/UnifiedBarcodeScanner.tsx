"use client";

import { useEffect, useRef, useState, useCallback } from "react";
import { Html5QrcodeScanner, Html5QrcodeScanType } from "html5-qrcode";
import { But<PERSON> } from "@/components/ui/button";
import { Flashlight, FlashlightOff } from "lucide-react";

interface UnifiedBarcodeScannerProps {
  onScan: (result: string) => void;
  onError?: (error: string) => void;
  onScanResult?: (result: string) => void; // New callback for populating search
}

export default function UnifiedBarcodeScanner({
  onScan,
  onError,
  onScanResult,
}: UnifiedBarcodeScannerProps) {
  const scannerRef = useRef<Html5QrcodeScanner | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<
    "requesting" | "granted" | "denied" | null
  >(null);
  const [scannedCode, setScannedCode] = useState<string | null>(null);
  const [torchEnabled, setTorchEnabled] = useState(false);
  const [torchSupported, setTorchSupported] = useState(false);

  // Detect mobile device
  const isMobile = useCallback(() => {
    return (
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      ) ||
      (navigator.maxTouchPoints && navigator.maxTouchPoints > 2)
    );
  }, []);

  // Request camera permission
  const requestCameraPermission = useCallback(async (): Promise<boolean> => {
    try {
      setPermissionStatus("requesting");
      console.log("🎥 Requesting camera permission...");

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment", // Prefer back camera on mobile
        },
      });

      // Stop the test stream immediately
      stream.getTracks().forEach((track) => track.stop());

      setPermissionStatus("granted");
      console.log("✅ Camera permission granted");
      return true;
    } catch (error) {
      console.error("❌ Camera permission denied:", error);
      setPermissionStatus("denied");
      setError(
        "Camera permission denied. Please allow camera access and refresh the page."
      );
      onError?.("Camera permission denied");
      return false;
    }
  }, [onError]);

  // Initialize scanner
  useEffect(() => {
    const initScanner = async () => {
      try {
        console.log("🎥 Initializing unified barcode scanner...");

        // Clean up any existing scanner first
        if (scannerRef.current) {
          try {
            await scannerRef.current.clear();
            scannerRef.current = null;
          } catch (error) {
            console.log("Previous scanner cleanup:", error);
          }
        }

        const readerElement = document.getElementById("unified-qr-reader");
        if (readerElement) {
          readerElement.innerHTML = "";
        }

        // Remove any existing custom styles
        const existingStyle = document.getElementById("unified-scanner-style");
        if (existingStyle) {
          existingStyle.remove();
        }

        // Add custom CSS to hide unwanted UI elements and optimize mobile
        const style = document.createElement("style");
        style.id = "unified-scanner-style";
        style.textContent = `
          /* Hide stop button and torch button (we'll use custom torch) */
          #unified-qr-reader button[title*="Stop"] {
            display: none !important;
          }

          #unified-qr-reader button[title*="torch"] {
            display: none !important;
          }

          /* Hide green banner and status messages */
          #unified-qr-reader__status_span {
            display: none !important;
          }

          /* Hide troubleshooting tips and help text */
          #unified-qr-reader__dashboard_section_csr > div:last-child {
            display: none !important;
          }
          

          /* Hide file selection option */
          #unified-qr-reader__filescan_input {
            display: none !important;
          }

          /* Hide camera selection dropdown - multiple selectors for robustness */
          #unified-qr-reader__camera_selection,
          #unified-qr-reader select,
          #unified-qr-reader__camera_permission_button,
          #unified-qr-reader__dashboard_section_csr select,
          .html5-qrcode-element select {
            display: none !important;
          }

          /* Hide any green success banners */
          .qr-code-success {
            display: none !important;
          }

          /* Start button will be hidden programmatically after auto-start succeeds */

          /* Enhanced mobile Chrome compatibility */
          @media (max-width: 768px) {
            #unified-qr-reader video {
              max-height: 250px !important;
              object-fit: cover !important;
              width: 100% !important;
            }

            #unified-qr-reader {
              min-height: 250px !important;
            }

            /* Force hide any remaining dropdowns on mobile */
            #unified-qr-reader select,
            #unified-qr-reader option {
              display: none !important;
            }
          }

          /* Additional UI cleanup for all screen sizes */
          #unified-qr-reader__dashboard_section_csr > div:nth-child(2),
          #unified-qr-reader__dashboard_section_csr > div:nth-child(3),
          #unified-qr-reader__dashboard_section_fsr,
          .html5-qrcode-element > div:last-child {
            display: none !important;
          }

          /* Clean scanner container */
          #unified-qr-reader {
            border-radius: 12px;
            overflow: hidden;
          }
        `;
        document.head.appendChild(style);

        // Request camera permissions first
        const hasPermission = await requestCameraPermission();
        if (!hasPermission) {
          return;
        }

        const mobile = isMobile();
        console.log("📱 Device type:", mobile ? "Mobile" : "Desktop");

        // Create scanner with optimized settings
        const scanner = new Html5QrcodeScanner(
          "unified-qr-reader",
          {
            fps: mobile ? 8 : 12,
            qrbox: mobile
              ? { width: 280, height: 160 }
              : { width: 350, height: 220 },
            aspectRatio: 1.777778,
            supportedScanTypes: [Html5QrcodeScanType.SCAN_TYPE_CAMERA],
            showTorchButtonIfSupported: false, // We'll handle torch with custom button
            showZoomSliderIfSupported: false,
            defaultZoomValueIfSupported: 2,
            rememberLastUsedCamera: true,
            experimentalFeatures: {
              useBarCodeDetectorIfSupported: true,
            },
            videoConstraints: mobile
              ? {
                  facingMode: "environment",
                  width: { ideal: 1280, max: 1920 },
                  height: { ideal: 600, max: 800 }, // Reduced height for mobile
                }
              : undefined,
          },
          false
        );

        scannerRef.current = scanner;

        const onScanSuccess = (decodedText: string) => {
          console.log("🎉 Barcode scanned successfully:", decodedText);
          setScannedCode(decodedText);

          // Call the new callback to populate search instead of auto-proceeding
          if (onScanResult) {
            onScanResult(decodedText);
          } else {
            // Fallback to old behavior if onScanResult not provided
            onScan(decodedText);
          }

          // Visual feedback
          const readerElement = document.getElementById("unified-qr-reader");
          if (readerElement) {
            readerElement.style.border = "3px solid #10B981";
            setTimeout(() => {
              readerElement.style.border = "2px solid #7c3aed";
            }, 1500);
          }
        };

        const onScanFailure = (error: string) => {
          // Only log significant errors, ignore common scanning noise
          if (
            !error.includes("No QR code found") &&
            !error.includes("NotFoundException") &&
            !error.includes("No MultiFormat Readers")
          ) {
            console.log("🔍 Scan error:", error);
          }
        };

        scanner.render(onScanSuccess, onScanFailure);
        setIsScanning(true);
        setError(null);
        console.log("✅ Scanner initialized and ready");

        // Auto-click the start button after a short delay with multiple attempts
        const autoStartScanning = () => {
          let attempts = 0;
          const maxAttempts = 15; // Increased for mobile Chrome

          const tryStart = () => {
            attempts++;
            console.log(`🎬 Auto-start attempt ${attempts}/${maxAttempts}...`);

            // Enhanced selectors for better mobile Chrome compatibility
            const selectors = [
              '#unified-qr-reader button:not([title*="Stop"]):not([title*="torch"]):not([style*="display: none"])',
              '#unified-qr-reader button[id*="start"]',
              '#unified-qr-reader button[id*="html5-qrcode-button-camera-start"]',
              "#unified-qr-reader button:visible",
              "#unified-qr-reader button",
              '#unified-qr-reader input[type="button"]',
              ".html5-qrcode-element button",
            ];

            for (const selector of selectors) {
              const buttons = document.querySelectorAll(
                selector
              ) as NodeListOf<HTMLButtonElement>;

              console.log(
                `🔍 Found ${buttons.length} buttons with selector: ${selector}`
              );

              for (const button of buttons) {
                const text = button.textContent?.toLowerCase() || "";
                const title = button.title?.toLowerCase() || "";
                const id = button.id?.toLowerCase() || "";

                // Enhanced button detection for mobile Chrome
                const isStartButton =
                  text.includes("start") ||
                  text.includes("scan") ||
                  text.includes("camera") ||
                  title.includes("start") ||
                  id.includes("start") ||
                  id.includes("camera");

                // Check if button is actually visible and clickable
                const isVisible =
                  button.offsetWidth > 0 && button.offsetHeight > 0;
                const isEnabled = !button.disabled;

                console.log(`🔍 Button analysis:`, {
                  text,
                  title,
                  id,
                  isStartButton,
                  isVisible,
                  isEnabled,
                });

                if (isStartButton && isVisible && isEnabled) {
                  console.log(
                    "✅ Found valid start button, clicking...",
                    button
                  );

                  // Force focus and click for mobile Chrome
                  button.focus();
                  button.click();

                  // Trigger additional events for mobile Chrome compatibility
                  button.dispatchEvent(
                    new MouseEvent("mousedown", { bubbles: true })
                  );
                  button.dispatchEvent(
                    new MouseEvent("mouseup", { bubbles: true })
                  );

                  // Hide the start button after clicking to maintain clean UI
                  setTimeout(() => {
                    button.style.display = "none";
                    console.log(
                      "🎯 Start button hidden after successful click"
                    );
                  }, 1000); // Increased delay for mobile

                  return true;
                }
              }
            }

            if (attempts < maxAttempts) {
              setTimeout(tryStart, 200);
            } else {
              console.log(
                "⚠️ Could not find start button after",
                maxAttempts,
                "attempts"
              );
            }
            return false;
          };

          setTimeout(tryStart, 300);
        };

        autoStartScanning();
      } catch (error) {
        console.error("❌ Scanner initialization failed:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Scanner initialization failed";
        setError(errorMessage);
        onError?.(errorMessage);
      }
    };

    initScanner();

    // Cleanup function
    return () => {
      if (scannerRef.current) {
        try {
          scannerRef.current.clear();
        } catch (error) {
          console.log("Scanner cleanup error:", error);
        }
      }
    };
  }, [onScan, onError, onScanResult, requestCameraPermission, isMobile]);

  // Toggle torch function
  const toggleTorch = useCallback(async () => {
    try {
      const video = document.querySelector(
        "#unified-qr-reader video"
      ) as HTMLVideoElement;
      if (video && video.srcObject) {
        const stream = video.srcObject as MediaStream;
        const track = stream.getVideoTracks()[0];

        if (track && "torch" in track.getCapabilities()) {
          await track.applyConstraints({
            advanced: [{ torch: !torchEnabled } as MediaTrackConstraints],
          });
          setTorchEnabled(!torchEnabled);
          console.log(`🔦 Torch ${!torchEnabled ? "enabled" : "disabled"}`);
        }
      }
    } catch (error) {
      console.log("Torch toggle failed:", error);
    }
  }, [torchEnabled]);

  // Check torch support with enhanced detection
  useEffect(() => {
    const checkTorchSupport = () => {
      const video = document.querySelector(
        "#unified-qr-reader video"
      ) as HTMLVideoElement;
      if (video && video.srcObject) {
        const stream = video.srcObject as MediaStream;
        const track = stream.getVideoTracks()[0];

        if (track) {
          const capabilities = track.getCapabilities();
          console.log("📱 Camera capabilities:", capabilities);

          if ("torch" in capabilities) {
            console.log("🔦 Torch support detected!");
            setTorchSupported(true);
          } else {
            console.log("❌ No torch support detected");
          }
        }
      }
    };

    // Only check if scanning and not already detected
    if (isScanning && !torchSupported) {
      // Check immediately and then periodically
      checkTorchSupport();

      const interval = setInterval(() => {
        checkTorchSupport();
        if (torchSupported) {
          clearInterval(interval);
        }
      }, 1000);

      // Clear after 10 seconds to avoid infinite checking
      const timeout = setTimeout(() => {
        clearInterval(interval);
        console.log("⏰ Torch detection timeout - stopping checks");
      }, 10000);

      return () => {
        clearInterval(interval);
        clearTimeout(timeout);
      };
    }
  }, [isScanning, torchSupported]);

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Permission status */}
      {permissionStatus === "requesting" && (
        <div className="mb-4 bg-blue-900 border border-blue-700 text-blue-300 px-4 py-3 rounded">
          <p className="text-sm">📷 Requesting camera permission...</p>
        </div>
      )}

      {permissionStatus === "denied" && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="text-sm">
            ❌ Camera access denied. Please allow camera access and refresh the
            page.
          </p>
        </div>
      )}

      {error && (
        <div className="mb-4 bg-red-900 border border-red-700 text-red-300 px-4 py-3 rounded">
          <p className="text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Scanned code display */}
      {scannedCode && (
        <div className="mb-4 bg-green-900 border border-green-700 text-green-300 px-4 py-3 rounded">
          <p className="text-sm font-bold">✅ Barcode Scanned!</p>
          <p className="text-xs font-mono bg-green-800 px-2 py-1 rounded mt-2">
            {scannedCode}
          </p>
          <p className="text-xs mt-2 text-green-400">
            Code has been populated in the search field above
          </p>
        </div>
      )}

      {/* Scanner container with torch button */}
      <div className="mb-4 relative">
        <div
          id="unified-qr-reader"
          className="border-2 border-purple-600 rounded-lg overflow-hidden"
          style={{ minHeight: "300px" }}
        />

        {/* Custom torch button */}
        {torchSupported && isScanning && (
          <div className="absolute top-4 right-4">
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleTorch}
              className="bg-black/50 hover:bg-black/70 text-white border-white/20"
            >
              {torchEnabled ? (
                <FlashlightOff className="h-4 w-4" />
              ) : (
                <Flashlight className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Status indicator */}
      {isScanning && permissionStatus === "granted" && !scannedCode && (
        <div className="text-center text-sm text-gray-400">
          <p>📱 Point your camera at a barcode to scan</p>
        </div>
      )}
    </div>
  );
}
