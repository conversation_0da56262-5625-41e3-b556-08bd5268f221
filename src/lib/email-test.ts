// Email testing utilities for development and debugging
import { Item } from './supabase'
import { emailTemplates, emailAlerts, alertCheckers } from './email'

// Mock item data for testing
export const mockItems: Item[] = [
  {
    id: 1,
    upc: '123456789012',
    description: 'Test Low Stock Item',
    quantity: 2,
    minimum: 5,
    product_image: 'https://example.com/image1.jpg',
    purchase_link: 'https://example.com/purchase1',
    suggested_brand_model: 'Brand A Model X',
    suggested_purchase_qty: 10,
    notes: 'This is a test item for low stock alerts',
    count_interval_days: 30,
    date_last_counted: '2024-05-01T00:00:00Z', // Old date to trigger cycle count
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-06-26T00:00:00Z'
  },
  {
    id: 2,
    upc: '987654321098',
    description: 'Test Cycle Count Item',
    quantity: 15,
    minimum: 10,
    product_image: 'https://example.com/image2.jpg',
    purchase_link: 'https://example.com/purchase2',
    suggested_brand_model: 'Brand B Model Y',
    suggested_purchase_qty: 20,
    notes: 'This item needs cycle counting',
    count_interval_days: 30,
    date_last_counted: '2024-04-01T00:00:00Z', // Very old date
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-06-26T00:00:00Z'
  },
  {
    id: 3,
    upc: '555666777888',
    description: 'Test Normal Item',
    quantity: 25,
    minimum: 10,
    product_image: undefined,
    purchase_link: undefined,
    suggested_brand_model: undefined,
    suggested_purchase_qty: undefined,
    notes: undefined,
    count_interval_days: undefined,
    date_last_counted: '2024-06-20T00:00:00Z', // Recent date
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-06-26T00:00:00Z'
  }
]

// Test functions
export const emailTests = {
  // Test email template generation
  testTemplates(): void {
    console.log('🧪 Testing Email Templates...\n')
    
    mockItems.forEach((item, index) => {
      console.log(`--- Item ${index + 1}: ${item.description} ---`)
      
      // Test low stock template
      const lowStockTemplate = emailTemplates.lowStockAlert(item)
      console.log('Low Stock Subject:', lowStockTemplate.subject)
      console.log('Low Stock Text Preview:', lowStockTemplate.text.substring(0, 100) + '...')
      
      // Test cycle count template
      const cycleCountTemplate = emailTemplates.cycleCountAlert(item)
      console.log('Cycle Count Subject:', cycleCountTemplate.subject)
      console.log('Cycle Count Text Preview:', cycleCountTemplate.text.substring(0, 100) + '...')
      console.log('')
    })
  },

  // Test alert checking logic
  async testAlertCheckers(): Promise<void> {
    console.log('🧪 Testing Alert Checkers...\n')
    
    for (const item of mockItems) {
      console.log(`--- Item: ${item.description} ---`)
      console.log(`Quantity: ${item.quantity}, Minimum: ${item.minimum}`)
      console.log(`Last Counted: ${item.date_last_counted}`)
      
      try {
        const shouldSendLowStock = await alertCheckers.shouldSendLowStockAlert(item)
        const shouldSendCycleCount = await alertCheckers.shouldSendCycleCountAlert(item)
        
        console.log(`Should send low stock alert: ${shouldSendLowStock}`)
        console.log(`Should send cycle count alert: ${shouldSendCycleCount}`)
      } catch (error) {
        console.log(`Error checking alerts: ${error}`)
      }
      console.log('')
    }
  },

  // Test email sending (will only work with valid API keys)
  async testEmailSending(itemIndex: number = 0): Promise<void> {
    console.log('🧪 Testing Email Sending...\n')
    
    if (itemIndex >= mockItems.length) {
      console.log('❌ Invalid item index')
      return
    }
    
    const item = mockItems[itemIndex]
    console.log(`Testing with item: ${item.description}`)
    
    try {
      // Test low stock alert
      console.log('Sending low stock alert...')
      const lowStockResult = await emailAlerts.sendLowStockAlert(item)
      console.log(`Low stock alert result: ${lowStockResult}`)
      
      // Test cycle count alert
      console.log('Sending cycle count alert...')
      const cycleCountResult = await emailAlerts.sendCycleCountAlert(item)
      console.log(`Cycle count alert result: ${cycleCountResult}`)
      
    } catch (error) {
      console.log(`❌ Error testing email sending: ${error}`)
    }
  },

  // Generate sample HTML for preview
  generateSampleHTML(): string {
    const item = mockItems[0]
    const lowStockTemplate = emailTemplates.lowStockAlert(item)
    
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Email Preview</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .email-preview { border: 1px solid #ccc; padding: 20px; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Email Template Preview</h1>
    
    <div class="email-preview">
        <h3>Low Stock Alert</h3>
        ${lowStockTemplate.html}
    </div>
    
    <div class="email-preview">
        <h3>Cycle Count Alert</h3>
        ${emailTemplates.cycleCountAlert(item).html}
    </div>
</body>
</html>
    `
  }
}

// Console helper for easy testing
export const runEmailTests = async () => {
  console.log('🚀 Running Email System Tests...\n')
  
  // Test templates
  emailTests.testTemplates()
  
  // Test alert checkers (this will fail without database connection)
  try {
    await emailTests.testAlertCheckers()
  } catch (error) {
    console.log('⚠️ Alert checker tests failed (expected without database):', error)
  }
  
  console.log('✅ Email tests completed!')
}
