import nodemailer from "nodemailer";
import { supabase, Item } from "./supabase";

// Email configuration
const EMAIL_CONFIG = {
  from: process.env.EMAIL_FROM || "<EMAIL>",
  to: process.env.EMAIL_TO || "<EMAIL>",
  defaultCycleCountDays: parseInt(process.env.CYCLE_COUNT_DAYS || "30"),
  // SMTP Configuration
  smtp: {
    host: process.env.SMTP_HOST || "smtp.gmail.com",
    port: parseInt(process.env.SMTP_PORT || "587"),
    secure: process.env.SMTP_SECURE === "true", // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER || process.env.EMAIL_FROM,
      pass: process.env.SMTP_PASS || "your-app-password",
    },
  },
};

// Create reusable transporter object using SMTP transport
const createTransporter = () => {
  return nodemailer.createTransport({
    host: EMAIL_CONFIG.smtp.host,
    port: EMAIL_CONFIG.smtp.port,
    secure: EMAIL_CONFIG.smtp.secure,
    auth: {
      user: EMAIL_CONFIG.smtp.auth.user,
      pass: EMAIL_CONFIG.smtp.auth.pass,
    },
  });
};

// Interface for email notification tracking
export interface EmailNotification {
  id?: number;
  upc: string;
  notification_type:
    | "low_stock"
    | "cycle_count"
    | "weekly_cycle_summary"
    | "weekly_purchase_reminder";
  email_sent_at: string;
  email_status?: string;
  email_content?: object;
}

// Email notification API
export const emailNotificationsApi = {
  async create(
    notification: Omit<EmailNotification, "id">
  ): Promise<EmailNotification> {
    const { data, error } = await supabase
      .from("email_notifications")
      .insert([notification])
      .select()
      .single();

    if (error) {
      console.error("❌ Failed to create email notification record:", error);
      throw new Error(`Failed to create email notification: ${error.message}`);
    }

    return data;
  },

  async getLastNotification(
    itemUpc: string,
    type: "low_stock" | "cycle_count"
  ): Promise<EmailNotification | null> {
    const { data, error } = await supabase
      .from("email_notifications")
      .select("*")
      .eq("upc", itemUpc)
      .eq("notification_type", type)
      .order("email_sent_at", { ascending: false })
      .limit(1)
      .single();

    if (error) {
      if (error.code === "PGRST116") {
        // No rows found
        return null;
      }
      console.error("❌ Failed to get last notification:", error);
      throw new Error(`Failed to get last notification: ${error.message}`);
    }

    return data;
  },
};

// Email template functions
export const emailTemplates = {
  lowStockAlert(item: Item): { subject: string; text: string; html: string } {
    const subject = `Low Stock Alert for ${item.description}`;

    const text = `The item ${item.description} (UPC: ${
      item.upc
    }) is low in stock.
Current Quantity: ${item.quantity || 0}
Minimum Required: ${item.minimum || "Not set"}
Product Image: ${item.product_image || "No image available"}
Purchase Link: ${item.purchase_link || "No link available"}
Suggested Brand/Model: ${item.suggested_brand_model || "Not specified"}
Suggested Purchase Quantity: ${item.suggested_purchase_qty || "Not specified"}
Notes: ${item.notes || "No additional notes."}`;

    const html = `
      <h2>🚨 Low Stock Alert</h2>
      <p>The item <strong>${item.description}</strong> (UPC: ${
      item.upc
    }) is low in stock.</p>

      ${
        item.product_image
          ? `
      <div style="text-align: center; margin: 20px 0;">
        <img src="${item.product_image}" alt="${item.description}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;" />
      </div>
      `
          : ""
      }

      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Current Quantity:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.quantity || 0
          }</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Minimum Required:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.minimum || "Not set"
          }</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Suggested Brand/Model:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.suggested_brand_model || "Not specified"
          }</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Suggested Purchase Quantity:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.suggested_purchase_qty || "Not specified"
          }</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Notes:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.notes || "No additional notes."
          }</td>
        </tr>
      </table>

      ${
        item.purchase_link
          ? `
      <div style="text-align: center; margin: 20px 0;">
        <a href="${item.purchase_link}" target="_blank" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">🛒 Purchase This Item</a>
      </div>
      `
          : ""
      }

      <p><em>This alert was generated automatically by the ISC Inventory Management System.</em></p>
    `;

    return { subject, text, html };
  },

  weeklyCycleSummary(items: Item[]): {
    subject: string;
    text: string;
    html: string;
  } {
    const subject = `Weekly Cycle Count Summary - ${items.length} Items Need Counting`;

    const itemList = items
      .map((item) => {
        const daysSinceCount = item.date_last_counted
          ? Math.floor(
              (Date.now() - new Date(item.date_last_counted).getTime()) /
                (1000 * 60 * 60 * 24)
            )
          : "Never";

        return `• ${item.description} (UPC: ${item.upc})
  Current Qty: ${item.quantity || 0}
  Last Counted: ${
    item.date_last_counted
      ? new Date(item.date_last_counted).toLocaleDateString()
      : "Never"
  } ${daysSinceCount !== "Never" ? `(${daysSinceCount} days ago)` : ""}
  Cycle Interval: ${
    item.count_interval_days || EMAIL_CONFIG.defaultCycleCountDays
  } days`;
      })
      .join("\n\n");

    const text = `Weekly Cycle Count Summary

The following ${items.length} items need to be cycle counted:

${itemList}

Please schedule time to count these items to maintain accurate inventory levels.`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">📊 Weekly Cycle Count Summary</h2>
        <p>The following <strong>${
          items.length
        } items</strong> need to be cycle counted:</p>

        <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
          ${items
            .map((item) => {
              const daysSinceCount = item.date_last_counted
                ? Math.floor(
                    (Date.now() - new Date(item.date_last_counted).getTime()) /
                      (1000 * 60 * 60 * 24)
                  )
                : "Never";

              return `
              <div style="border-bottom: 1px solid #e5e7eb; padding: 10px 0; margin-bottom: 10px;">
                <h4 style="margin: 0; color: #1f2937;">${item.description}</h4>
                <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">UPC: ${
                  item.upc
                }</p>
                <div style="display: flex; gap: 20px; margin-top: 8px;">
                  <span style="color: #059669;"><strong>Current Qty:</strong> ${
                    item.quantity || 0
                  }</span>
                  <span style="color: #dc2626;"><strong>Last Counted:</strong> ${
                    item.date_last_counted
                      ? new Date(item.date_last_counted).toLocaleDateString()
                      : "Never"
                  } ${
                daysSinceCount !== "Never" ? `(${daysSinceCount} days ago)` : ""
              }</span>
                  <span style="color: #7c3aed;"><strong>Cycle Interval:</strong> ${
                    item.count_interval_days ||
                    EMAIL_CONFIG.defaultCycleCountDays
                  } days</span>
                </div>
              </div>
            `;
            })
            .join("")}
        </div>

        <p style="color: #6b7280; font-size: 14px;">Please schedule time to count these items to maintain accurate inventory levels.</p>
      </div>
    `;

    return { subject, text, html };
  },

  weeklyPurchaseReminder(items: Item[]): {
    subject: string;
    text: string;
    html: string;
  } {
    const subject = `Weekly Purchase Reminder - ${items.length} Items at Minimum Stock`;

    const itemList = items
      .map((item) => {
        return `• ${item.description} (UPC: ${item.upc})
  Current Qty: ${item.quantity || 0}
  Minimum: ${item.minimum}
  Suggested Purchase Qty: ${item.suggested_purchase_qty || "Not specified"}
  Purchase Link: ${item.purchase_link || "No link available"}`;
      })
      .join("\n\n");

    const text = `Weekly Purchase Reminder

The following ${items.length} items are at minimum stock levels and may need to be purchased:

${itemList}

Please review and consider placing orders for these items.`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #dc2626;">🛒 Weekly Purchase Reminder</h2>
        <p>The following <strong>${
          items.length
        } items</strong> are at minimum stock levels and may need to be purchased:</p>

        <div style="background-color: #fef2f2; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626;">
          ${items
            .map(
              (item) => `
            <div style="border-bottom: 1px solid #fecaca; padding: 10px 0; margin-bottom: 10px;">
              <h4 style="margin: 0; color: #1f2937;">${item.description}</h4>
              <p style="margin: 5px 0; color: #6b7280; font-size: 14px;">UPC: ${
                item.upc
              }</p>
              <div style="display: flex; gap: 20px; margin-top: 8px; flex-wrap: wrap;">
                <span style="color: #dc2626;"><strong>Current Qty:</strong> ${
                  item.quantity || 0
                }</span>
                <span style="color: #dc2626;"><strong>Minimum:</strong> ${
                  item.minimum
                }</span>
                <span style="color: #059669;"><strong>Suggested Purchase:</strong> ${
                  item.suggested_purchase_qty || "Not specified"
                }</span>
              </div>
              ${
                item.purchase_link
                  ? `<p style="margin-top: 8px;"><a href="${item.purchase_link}" style="color: #2563eb; text-decoration: none;">🔗 Purchase Link</a></p>`
                  : ""
              }
            </div>
          `
            )
            .join("")}
        </div>

        <p style="color: #6b7280; font-size: 14px;">Please review and consider placing orders for these items.</p>
      </div>
    `;

    return { subject, text, html };
  },

  cycleCountAlert(item: Item): { subject: string; text: string; html: string } {
    const subject = `Cycle Count Alert for ${item.description}`;

    const text = `The item ${item.description} (UPC: ${item.upc}) needs counted.
Current Quantity: ${item.quantity || 0}
Product Image: ${item.product_image || "No image available"}
Suggested Brand/Model: ${item.suggested_brand_model || "Not specified"}
Notes: ${item.notes || "No additional notes."}`;

    const html = `
      <h2>📊 Cycle Count Alert</h2>
      <p>The item <strong>${item.description}</strong> (UPC: ${
      item.upc
    }) needs counted.</p>

      ${
        item.product_image
          ? `
      <div style="text-align: center; margin: 20px 0;">
        <img src="${item.product_image}" alt="${item.description}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;" />
      </div>
      `
          : ""
      }

      <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Current Quantity:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.quantity || 0
          }</td>
        </tr>
        <tr>
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Suggested Brand/Model:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.suggested_brand_model || "Not specified"
          }</td>
        </tr>
        <tr style="background-color: #f5f5f5;">
          <td style="padding: 8px; border: 1px solid #ddd; font-weight: bold;">Notes:</td>
          <td style="padding: 8px; border: 1px solid #ddd;">${
            item.notes || "No additional notes."
          }</td>
        </tr>
      </table>

      <p><em>This alert was generated automatically by the ISC Inventory Management System.</em></p>
    `;

    return { subject, text, html };
  },
};

// Alert checking functions
export const alertCheckers = {
  async shouldSendLowStockAlert(item: Item): Promise<boolean> {
    console.log(`🔍 Checking low stock alert for ${item.description}:`);
    console.log(`  - Quantity: ${item.quantity || 0}`);
    console.log(`  - Minimum: ${item.minimum || "not set"}`);

    // Check if item has minimum set and current quantity is at or below minimum
    if (!item.minimum || (item.quantity || 0) > item.minimum) {
      console.log(
        `  - ❌ No alert needed: quantity (${item.quantity || 0}) > minimum (${
          item.minimum || "not set"
        })`
      );
      return false;
    }

    console.log(
      `  - ✅ Low stock condition met: quantity (${
        item.quantity || 0
      }) <= minimum (${item.minimum})`
    );

    // Check if we've already sent a low stock alert recently (within 24 hours)
    try {
      const lastNotification = await emailNotificationsApi.getLastNotification(
        item.upc,
        "low_stock"
      );
      if (lastNotification) {
        const lastSentTime = new Date(lastNotification.email_sent_at);
        const now = new Date();
        const hoursSinceLastAlert =
          (now.getTime() - lastSentTime.getTime()) / (1000 * 60 * 60);

        // Don't send another alert if one was sent within the last 24 hours
        if (hoursSinceLastAlert < 24) {
          console.log(
            `  - ⏰ Low stock alert for ${
              item.description
            } was already sent ${hoursSinceLastAlert.toFixed(1)} hours ago`
          );
          return false;
        } else {
          console.log(
            `  - ✅ Last alert was ${hoursSinceLastAlert.toFixed(
              1
            )} hours ago, can send new alert`
          );
        }
      } else {
        console.log(`  - ✅ No previous low stock alerts found`);
      }
    } catch (error) {
      console.error(
        "  - ❌ Error checking last low stock notification:",
        error
      );
      // If we can't check, err on the side of not sending to avoid spam
      return false;
    }

    console.log(`  - ✅ Should send low stock alert: YES`);
    return true;
  },

  async shouldSendCycleCountAlert(item: Item): Promise<boolean> {
    // Get the cycle count interval (use item-specific or default)
    const cycleCountDays =
      item.count_interval_days || EMAIL_CONFIG.defaultCycleCountDays;

    // Check if date_last_counted is older than the cycle count interval
    if (!item.date_last_counted) {
      // If never counted, check if item is old enough to warrant counting
      const createdDate = new Date(item.created_at);
      const now = new Date();
      const daysSinceCreated =
        (now.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24);

      if (daysSinceCreated < cycleCountDays) {
        return false;
      }
    } else {
      const lastCountedDate = new Date(item.date_last_counted);
      const now = new Date();
      const daysSinceLastCount =
        (now.getTime() - lastCountedDate.getTime()) / (1000 * 60 * 60 * 24);

      if (daysSinceLastCount < cycleCountDays) {
        return false;
      }
    }

    // Note: Cycle count email duplicate prevention is handled by the separate cycle count system
    // Since email_notifications table only supports 'low_stock', we rely on the cycle count
    // interval logic and item's date_last_counted field for duplicate prevention
    console.log(`✅ Cycle count alert should be sent for ${item.description}`);

    return true;
  },
};

// Weekly summary functions
export const weeklySummaries = {
  async getItemsNeedingCycleCount(): Promise<Item[]> {
    console.log("🔍 Getting items that need cycle counting...");

    const { data: items, error } = await supabase.from("items").select("*");

    if (error) {
      console.error("❌ Error fetching items for cycle count check:", error);
      throw new Error(`Failed to fetch items: ${error.message}`);
    }

    const itemsNeedingCount = items.filter((item) => {
      const cycleCountDays =
        item.count_interval_days || EMAIL_CONFIG.defaultCycleCountDays;

      if (!item.date_last_counted) {
        return true; // Never counted
      }

      const lastCountedDate = new Date(item.date_last_counted);
      const daysSinceCount = Math.floor(
        (Date.now() - lastCountedDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      return daysSinceCount >= cycleCountDays;
    });

    console.log(
      `📊 Found ${itemsNeedingCount.length} items needing cycle count`
    );
    return itemsNeedingCount;
  },

  async getItemsAtMinimumStock(): Promise<Item[]> {
    console.log("🔍 Getting items at minimum stock levels...");

    const { data: items, error } = await supabase
      .from("items")
      .select("*")
      .not("minimum", "is", null);

    if (error) {
      console.error(
        "❌ Error fetching items for purchase reminder check:",
        error
      );
      throw new Error(`Failed to fetch items: ${error.message}`);
    }

    const itemsAtMinimum = items.filter((item) => {
      const quantity = item.quantity || 0;
      const minimum = item.minimum || 0;
      return quantity <= minimum;
    });

    console.log(`🛒 Found ${itemsAtMinimum.length} items at minimum stock`);
    return itemsAtMinimum;
  },

  async shouldSendWeeklySummary(
    type: "cycle_count" | "purchase_reminder"
  ): Promise<boolean> {
    console.log(`🔍 Checking if weekly ${type} summary should be sent...`);

    // Check if we've sent this type of summary in the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const { data: recentNotifications, error } = await supabase
      .from("email_notifications")
      .select("*")
      .eq(
        "notification_type",
        type === "cycle_count"
          ? "weekly_cycle_summary"
          : "weekly_purchase_reminder"
      )
      .gte("email_sent_at", sevenDaysAgo.toISOString())
      .order("email_sent_at", { ascending: false })
      .limit(1);

    if (error) {
      console.error(`❌ Error checking recent ${type} notifications:`, error);
      return false; // Err on the side of not sending to avoid spam
    }

    const shouldSend = recentNotifications.length === 0;
    console.log(`📧 Should send weekly ${type} summary: ${shouldSend}`);
    return shouldSend;
  },
};

// Main email sending functions
export const emailAlerts = {
  async sendLowStockAlert(item: Item): Promise<boolean> {
    try {
      console.log(`📧 Sending low stock alert for ${item.description}`);

      const template = emailTemplates.lowStockAlert(item);
      const transporter = createTransporter();

      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.to,
        subject: template.subject,
        text: template.text,
        html: template.html,
      };

      const info = await transporter.sendMail(mailOptions);
      console.log("✅ Low stock email sent successfully:", info.messageId);

      // Record the notification in the database
      await emailNotificationsApi.create({
        upc: item.upc,
        notification_type: "low_stock",
        email_sent_at: new Date().toISOString(),
        email_status: "sent",
        email_content: {
          subject: template.subject,
          messageId: info.messageId,
        },
      });

      return true;
    } catch (error) {
      console.error("❌ Error sending low stock alert:", error);
      return false;
    }
  },

  async sendCycleCountAlert(item: Item): Promise<boolean> {
    try {
      console.log(`📧 Sending cycle count alert for ${item.description}`);

      const template = emailTemplates.cycleCountAlert(item);
      const transporter = createTransporter();

      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.to,
        subject: template.subject,
        text: template.text,
        html: template.html,
      };

      const info = await transporter.sendMail(mailOptions);
      console.log("✅ Cycle count email sent successfully:", info.messageId);

      // Note: Cycle count email tracking is handled separately since the email_notifications
      // table constraint only supports 'low_stock' notifications
      console.log(
        "📝 Cycle count email tracking: Using separate cycle count system"
      );

      return true;
    } catch (error) {
      console.error("❌ Error sending cycle count alert:", error);
      return false;
    }
  },

  async checkAndSendAlerts(item: Item): Promise<void> {
    console.log(
      `📧 Checking email alerts for item: ${item.description} (UPC: ${item.upc})`
    );

    try {
      // Check and send low stock alert
      console.log(`🔍 Checking low stock alert...`);
      if (await alertCheckers.shouldSendLowStockAlert(item)) {
        console.log(`📧 Sending low stock alert...`);
        await this.sendLowStockAlert(item);
      } else {
        console.log(`⏭️ Skipping low stock alert`);
      }

      // NOTE: Individual cycle count alerts are now disabled in favor of weekly summaries
      // Use emailAlerts.sendWeeklySummaries() for cycle count notifications
      console.log(
        `⏭️ Skipping individual cycle count alert (using weekly summaries instead)`
      );
    } catch (error) {
      console.error("❌ Error checking and sending alerts:", error);
    }

    console.log(`✅ Email alert check completed for ${item.description}`);
  },

  async sendWeeklyCycleSummary(): Promise<boolean> {
    try {
      console.log("📧 Checking weekly cycle count summary...");

      // Check if we should send the summary
      if (!(await weeklySummaries.shouldSendWeeklySummary("cycle_count"))) {
        console.log("⏭️ Weekly cycle count summary already sent recently");
        return false;
      }

      // Get items that need cycle counting
      const items = await weeklySummaries.getItemsNeedingCycleCount();

      if (items.length === 0) {
        console.log("✅ No items need cycle counting - skipping summary");
        return false;
      }

      console.log(
        `📧 Sending weekly cycle count summary for ${items.length} items`
      );

      const template = emailTemplates.weeklyCycleSummary(items);
      const transporter = createTransporter();

      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.to,
        subject: template.subject,
        text: template.text,
        html: template.html,
      };

      const info = await transporter.sendMail(mailOptions);
      console.log(
        "✅ Weekly cycle count summary sent successfully:",
        info.messageId
      );

      // Record the notification in the database (use first item's UPC as representative)
      await emailNotificationsApi.create({
        upc: items[0].upc, // Use first item as representative
        notification_type: "weekly_cycle_summary",
        email_sent_at: new Date().toISOString(),
        email_status: "sent",
        email_content: {
          subject: template.subject,
          messageId: info.messageId,
          itemCount: items.length,
          itemUpcs: items.map((item) => item.upc),
        },
      });

      return true;
    } catch (error) {
      console.error("❌ Error sending weekly cycle count summary:", error);
      return false;
    }
  },

  async sendWeeklyPurchaseReminder(): Promise<boolean> {
    try {
      console.log("📧 Checking weekly purchase reminder...");

      // Check if we should send the reminder
      if (
        !(await weeklySummaries.shouldSendWeeklySummary("purchase_reminder"))
      ) {
        console.log("⏭️ Weekly purchase reminder already sent recently");
        return false;
      }

      // Get items at minimum stock
      const items = await weeklySummaries.getItemsAtMinimumStock();

      if (items.length === 0) {
        console.log(
          "✅ No items at minimum stock - skipping purchase reminder"
        );
        return false;
      }

      console.log(
        `📧 Sending weekly purchase reminder for ${items.length} items`
      );

      const template = emailTemplates.weeklyPurchaseReminder(items);
      const transporter = createTransporter();

      const mailOptions = {
        from: EMAIL_CONFIG.from,
        to: EMAIL_CONFIG.to,
        subject: template.subject,
        text: template.text,
        html: template.html,
      };

      const info = await transporter.sendMail(mailOptions);
      console.log(
        "✅ Weekly purchase reminder sent successfully:",
        info.messageId
      );

      // Record the notification in the database (use first item's UPC as representative)
      await emailNotificationsApi.create({
        upc: items[0].upc, // Use first item as representative
        notification_type: "weekly_purchase_reminder",
        email_sent_at: new Date().toISOString(),
        email_status: "sent",
        email_content: {
          subject: template.subject,
          messageId: info.messageId,
          itemCount: items.length,
          itemUpcs: items.map((item) => item.upc),
        },
      });

      return true;
    } catch (error) {
      console.error("❌ Error sending weekly purchase reminder:", error);
      return false;
    }
  },

  async sendWeeklySummaries(): Promise<{
    cycleSummary: boolean;
    purchaseReminder: boolean;
  }> {
    console.log("📧 Starting weekly summaries check...");

    const results = {
      cycleSummary: false,
      purchaseReminder: false,
    };

    try {
      // Send cycle count summary
      results.cycleSummary = await this.sendWeeklyCycleSummary();

      // Send purchase reminder
      results.purchaseReminder = await this.sendWeeklyPurchaseReminder();

      console.log("✅ Weekly summaries check completed:", results);
      return results;
    } catch (error) {
      console.error("❌ Error in weekly summaries:", error);
      return results;
    }
  },
};
