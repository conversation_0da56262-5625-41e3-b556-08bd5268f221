{"name": "inv-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.50.0", "@types/nodemailer": "^6.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html5-qrcode": "^2.3.8", "lucide-react": "^0.525.0", "next": "15.3.3", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}