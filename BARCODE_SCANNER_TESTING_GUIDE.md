# Barcode Scanner Testing Guide

## Why <PERSON><PERSON> Can't Test Camera Features

**Security Restrictions**: Browser automation tools like <PERSON><PERSON> cannot access camera APIs due to security policies:
- Camera access requires explicit user permission
- Automated browsers run in sandboxed environments
- MediaDevices API is restricted in headless mode
- Camera permissions can't be programmatically granted in automation

**Alternative Testing Approaches**:

## 1. Manual Testing Protocol

### Chrome Mobile Testing (Primary Concern)
```bash
# Access via mobile Chrome
https://your-domain.com

# Test Steps:
1. Open scanner page
2. Check browser console for logs:
   - "🎥 Requesting camera permission..."
   - "✅ Scanner initialized and ready"
   - "🎬 Auto-start attempt X/15..."
   - "✅ Found valid start button, clicking..."
   - "🔦 Torch support detected!" (if available)

3. Verify video preview appears
4. Test torch button (if visible)
5. Test barcode scanning functionality
```

### Firefox Laptop Testing
```bash
# Access via Firefox
https://your-domain.com

# Verify:
- Video preview works
- Auto-start functions
- UI elements are properly hidden
- Torch detection works (if supported)
```

### Edge Browser Testing
```bash
# Access via Edge
https://your-domain.com

# Company standard browser testing
- Ensure compatibility with corporate environment
- Test camera permissions flow
- Verify UI consistency
```

## 2. Debug Console Commands

### Check Camera Permissions
```javascript
// Run in browser console
navigator.mediaDevices.getUserMedia({video: true})
  .then(stream => {
    console.log("✅ Camera access granted");
    stream.getTracks().forEach(track => track.stop());
  })
  .catch(err => console.error("❌ Camera access denied:", err));
```

### Check Torch Support
```javascript
// Run after scanner is active
const video = document.querySelector('#unified-qr-reader video');
if (video && video.srcObject) {
  const track = video.srcObject.getVideoTracks()[0];
  console.log("Camera capabilities:", track.getCapabilities());
  console.log("Torch supported:", 'torch' in track.getCapabilities());
}
```

### Debug Button Detection
```javascript
// Check what buttons are found
const selectors = [
  '#unified-qr-reader button:not([title*="Stop"]):not([title*="torch"])',
  '#unified-qr-reader button',
  '.html5-qrcode-element button'
];

selectors.forEach(selector => {
  const buttons = document.querySelectorAll(selector);
  console.log(`Selector "${selector}": ${buttons.length} buttons found`);
  buttons.forEach((btn, i) => {
    console.log(`Button ${i}:`, {
      text: btn.textContent,
      id: btn.id,
      visible: btn.offsetWidth > 0,
      enabled: !btn.disabled
    });
  });
});
```

## 3. Network-Based Testing

### Local Network Testing
```bash
# Start dev server with network access
npm run dev

# Access from mobile device on same network
http://************:3000

# Benefits:
- Real mobile device testing
- Actual camera hardware
- Real touch interactions
- Authentic mobile Chrome behavior
```

### Remote Testing Services
```bash
# Use services like:
- BrowserStack (real device testing)
- Sauce Labs (mobile browser testing)
- LambdaTest (cross-browser testing)

# These provide real mobile devices with cameras
```

## 4. Automated Testing Alternatives

### Unit Tests for Logic
```javascript
// Test non-camera functionality
describe('Scanner Logic', () => {
  test('button detection logic', () => {
    // Test button finding algorithms
  });
  
  test('mobile device detection', () => {
    // Test isMobile() function
  });
  
  test('torch capability detection', () => {
    // Mock MediaStreamTrack capabilities
  });
});
```

### Integration Tests
```javascript
// Test UI components without camera
describe('Scanner UI', () => {
  test('renders scanner container', () => {
    // Test component rendering
  });
  
  test('shows permission status', () => {
    // Test permission state UI
  });
  
  test('displays torch button when supported', () => {
    // Mock torch support and test UI
  });
});
```

## 5. Performance Testing

### Measure Scanner Initialization
```javascript
// Add to component for timing
console.time('scanner-init');
// ... scanner initialization code ...
console.timeEnd('scanner-init');
```

### Monitor Memory Usage
```javascript
// Check for memory leaks
setInterval(() => {
  if (performance.memory) {
    console.log('Memory usage:', {
      used: Math.round(performance.memory.usedJSHeapSize / 1048576) + 'MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1048576) + 'MB'
    });
  }
}, 5000);
```

## 6. Troubleshooting Common Issues

### Mobile Chrome Video Not Showing
1. Check console for permission errors
2. Verify auto-start button detection logs
3. Test manual camera permission grant
4. Check for conflicting CSS rules
5. Verify video element creation

### Torch Button Not Appearing
1. Check torch capability detection logs
2. Verify device supports flashlight
3. Test on different mobile devices
4. Check torch support timing

### Camera Selection Dropdown Visible
1. Verify CSS rules are applied
2. Check for dynamic content loading
3. Test selector specificity
4. Add additional hiding rules if needed

## 7. Deployment Testing Checklist

- [ ] Test on actual mobile devices
- [ ] Verify HTTPS requirement for camera access
- [ ] Test camera permissions flow
- [ ] Verify video preview displays
- [ ] Test torch functionality (if supported)
- [ ] Check UI element hiding
- [ ] Test barcode scanning accuracy
- [ ] Verify performance on slower devices
- [ ] Test in different lighting conditions
- [ ] Verify cross-browser compatibility

## 8. Monitoring in Production

### Error Tracking
```javascript
// Add to scanner component
window.addEventListener('error', (event) => {
  if (event.filename?.includes('html5-qrcode')) {
    console.error('Scanner error:', event.error);
    // Send to error tracking service
  }
});
```

### Usage Analytics
```javascript
// Track scanner usage
analytics.track('scanner_initialized', {
  device: isMobile() ? 'mobile' : 'desktop',
  browser: navigator.userAgent,
  torch_supported: torchSupported
});
```

This comprehensive testing approach compensates for Playwright's camera limitations by providing thorough manual testing protocols and debugging tools.
