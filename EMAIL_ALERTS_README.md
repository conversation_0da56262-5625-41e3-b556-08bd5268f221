# Email Alert System Documentation

## Overview

The inventory system now includes automated email alerts for low stock and cycle count notifications. This system helps maintain optimal inventory levels and ensures regular cycle counting.

## Features

### 1. Low Stock Alerts
- **Trigger**: When an item's quantity falls to or below its minimum threshold
- **Frequency**: Maximum once per 24 hours per item
- **Content**: Current quantity, minimum required, purchase information, and item details

### 2. Cycle Count Alerts
- **Trigger**: When an item hasn't been counted within its cycle count interval (default: 30 days)
- **Frequency**: Maximum once per 7 days per item
- **Content**: Current quantity, last counted date, and item details

## Setup Instructions

### 1. Database Setup
Run the SQL migration to create the email notifications table:

```sql
-- Execute the contents of database/email_notifications_migration.sql
-- This creates the email_notifications table and necessary indexes
```

### 2. Email Configuration Options

#### Option A: Gmail SMTP (For Testing)

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a password for "Mail"
3. **Update `.env.local`**:

```env
# Gmail SMTP Configuration
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
CYCLE_COUNT_DAYS=30
```

#### Option B: Microsoft/Outlook SMTP (For Production)

1. **Use your company Microsoft 365 account**
2. **Update `.env.local`**:

```env
# Microsoft SMTP Configuration
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
CYCLE_COUNT_DAYS=30
```

#### Option C: Custom SMTP Server

```env
# Custom SMTP Configuration
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>
SMTP_HOST=mail.yourcompany.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
CYCLE_COUNT_DAYS=30
```

## Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `EMAIL_FROM` | Sender email address | `<EMAIL>` | Yes |
| `EMAIL_TO` | Recipient email address | `<EMAIL>` | Yes |
| `SMTP_HOST` | SMTP server hostname | `smtp.gmail.com` | Yes |
| `SMTP_PORT` | SMTP server port | `587` | Yes |
| `SMTP_SECURE` | Use SSL/TLS (true/false) | `false` | Yes |
| `SMTP_USER` | SMTP username (usually same as EMAIL_FROM) | - | Yes |
| `SMTP_PASS` | SMTP password or app password | - | Yes |
| `CYCLE_COUNT_DAYS` | Default cycle count interval | `30` | No |

### Item-Level Configuration

Each item can have its own cycle count interval:
- Set `count_interval_days` field on individual items
- If not set, uses the global `CYCLE_COUNT_DAYS` setting

## How It Works

### Automatic Triggers
1. **After Transactions**: Email alerts are checked after every successful transaction
2. **Low Stock Check**: If quantity ≤ minimum, and no alert sent in last 24 hours
3. **Cycle Count Check**: If last counted > interval days ago, and no alert sent in last 7 days

### Duplicate Prevention
- Tracks sent notifications in `email_notifications` table
- Prevents spam by enforcing minimum intervals between alerts
- Low stock: 24 hours minimum between alerts
- Cycle count: 7 days minimum between alerts

## API Endpoints

### POST /api/send-email
Send email alerts manually.

**Request Body:**
```json
{
  "itemUpc": "123456789012",
  "alertType": "low_stock" | "cycle_count" | "both"
}
```

**Response:**
```json
{
  "success": true,
  "item": {
    "upc": "123456789012",
    "description": "Item Name"
  },
  "results": {
    "lowStockSent": true,
    "cycleCountSent": false,
    "errors": []
  }
}
```

### GET /api/send-email?upc=123456789012
Check if alerts should be sent for an item.

**Response:**
```json
{
  "item": {
    "upc": "123456789012",
    "description": "Item Name",
    "quantity": 5,
    "minimum": 10,
    "date_last_counted": "2024-06-01T00:00:00Z"
  },
  "alerts": {
    "shouldSendLowStock": true,
    "shouldSendCycleCount": false,
    "lowStockReason": "Quantity (5) is at or below minimum (10)",
    "cycleCountReason": "Item was recently counted or cycle count not due"
  }
}
```

## Testing

### Development Testing
Use the email testing utilities:

```typescript
import { emailTests, runEmailTests } from '@/lib/email-test'

// Run all tests
await runEmailTests()

// Test individual components
emailTests.testTemplates()
await emailTests.testAlertCheckers()
await emailTests.testEmailSending(0) // Test with first mock item
```

### Manual Testing
1. Create test items with low quantities and old count dates
2. Perform transactions to trigger alerts
3. Check email delivery and database records
4. Verify duplicate prevention works

## Email Templates

### Low Stock Alert Template
- **Subject**: `Low Stock Alert for [Item Description]`
- **Content**: Formatted table with item details, purchase links, and quantities
- **Styling**: HTML table with alternating row colors

### Cycle Count Alert Template
- **Subject**: `Cycle Count Alert for [Item Description]`
- **Content**: Item details and current quantity information
- **Styling**: Clean HTML table format

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - **Gmail**: Ensure 2FA is enabled and you're using an App Password (not your regular password)
   - **Microsoft**: Verify your company allows SMTP access
   - **General**: Check SMTP credentials and server settings
   - Check console logs for detailed error messages

2. **Gmail-specific issues**
   - "Invalid login" error: Use App Password instead of regular password
   - "Less secure app access": Enable 2FA and use App Password
   - "Authentication failed": Double-check the 16-character App Password

3. **Microsoft/Outlook issues**
   - "Authentication failed": Some companies disable SMTP access
   - Contact IT department for SMTP server details
   - May need to use OAuth2 instead of basic auth (advanced setup)

4. **Duplicate alerts**
   - Check email_notifications table for recent entries
   - Verify time intervals are working correctly

5. **Database errors**
   - Ensure email_notifications table exists
   - Check foreign key constraints
   - Verify RLS policies allow operations

6. **SMTP Connection errors**
   - Verify SMTP_HOST and SMTP_PORT are correct
   - Check if your network/firewall blocks SMTP ports
   - Try different ports: 587 (TLS), 465 (SSL), 25 (plain)

### Debug Mode
Enable detailed logging by checking console output:
- Transaction processing logs
- Email alert checking logs
- Email sending results

## Security Considerations

1. **API Keys**: Store securely in environment variables
2. **Email Addresses**: Validate recipient addresses
3. **Rate Limiting**: Built-in duplicate prevention
4. **Database Access**: Uses existing Supabase RLS policies

## Future Enhancements

Potential improvements:
- Multiple recipient support
- Email templates customization UI
- Alert scheduling (specific times)
- SMS notifications
- Slack/Teams integration
- Dashboard for email analytics
